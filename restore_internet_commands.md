# Restore Internet Access cho VMs - <PERSON><PERSON>nh CMD cụ thể

## <PERSON><PERSON><PERSON> hình mục tiêu:
- **Host**: ************ (interface enp4s0)
- **Gateway VM**: ************* (virbr1)
- **cape1**: *************01 (IP tĩnh)
- **cuckoo1**: *************02 (IP tĩnh)

## BƯỚC 1: Tạo NAT Network (🖥️ CHẠY TRÊN HOST)

```bash
# Stop network hiện tại
sudo virsh net-destroy cape-hostonly
sudo virsh net-destroy cape-network

# Tạo NAT network XML
cat > /tmp/cape-nat.xml << 'EOF'
<network>
  <name>cape-nat</name>
  <forward mode='nat'>
    <nat>
      <port start='1024' end='65535'/>
    </nat>
  </forward>
  <bridge name='virbr1' stp='on' delay='0'/>
  <ip address='*************' netmask='*************'>
    <dhcp>
      <range start='**************' end='***************'/>
    </dhcp>
  </ip>
</network>
EOF

# Define và start NAT network
sudo virsh net-define /tmp/cape-nat.xml
sudo virsh net-start cape-nat
sudo virsh net-autostart cape-nat

# Verify network
sudo virsh net-list --all
```

## BƯỚC 2: Update VM Network (🖥️ CHẠY TRÊN HOST)

```bash
# Shutdown VMs
sudo virsh shutdown cape1
sudo virsh shutdown cuckoo1
sleep 15

# Update network cho cape1
sudo virsh detach-interface cape1 network --config
sudo virsh attach-interface cape1 network cape-nat --model virtio --config

# Update network cho cuckoo1
sudo virsh detach-interface cuckoo1 network --config
sudo virsh attach-interface cuckoo1 network cape-nat --model virtio --config

# Start VMs
sudo virsh start cape1
sudo virsh start cuckoo1

# Wait for VMs to boot
sleep 60

# Verify VMs are running
sudo virsh list
```

## BƯỚC 3: Setup NAT Rules (🖥️ CHẠY TRÊN HOST)

```bash
# Enable IP forwarding
echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward
echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf

# Remove old blocking rules
sudo iptables -D OUTPUT -p udp --dport 53 -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -p tcp --dport 53 -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -p tcp --dport 80 -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -p tcp --dport 443 -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -j DROP 2>/dev/null || true
sudo iptables -D OUTPUT -j REJECT 2>/dev/null || true

# Add NAT rules
sudo iptables -t nat -A POSTROUTING -s *************/24 -o enp4s0 -j MASQUERADE
sudo iptables -A FORWARD -s *************/24 -o enp4s0 -j ACCEPT
sudo iptables -A FORWARD -d *************/24 -i enp4s0 -m state --state RELATED,ESTABLISHED -j ACCEPT

# Allow VM communication
sudo iptables -A INPUT -s *************/24 -j ACCEPT
sudo iptables -A OUTPUT -d *************/24 -j ACCEPT

# Save rules
sudo iptables-save | sudo tee /etc/iptables/rules.v4

# Verify NAT rules
echo "NAT rules:"
sudo iptables -t nat -L -n | grep 192.168.100
echo "FORWARD rules:"
sudo iptables -L FORWARD -n | grep 192.168.100
```

## BƯỚC 4: Configure cape1 (💻 CHẠY TRONG VM cape1 - Windows 10)

```cmd
REM Mở Command Prompt as Administrator trong VM cape1

REM Set IP tĩnh *************01
netsh interface ip set address name="Ethernet" static *************01 ************* *************

REM Set DNS servers
netsh interface ip set dns name="Ethernet" static *******
netsh interface ip add dns name="Ethernet" ******* index=2

REM Flush DNS cache
ipconfig /flushdns

REM Test connectivity
ping -n 1 *************
ping -n 1 *******
ping -n 1 google.com

REM Test DNS resolution
nslookup google.com
nslookup github.com

REM Check final configuration
ipconfig /all
```

## BƯỚC 5: Configure cuckoo1 (💻 CHẠY TRONG VM cuckoo1 - Windows 7)

```cmd
REM Mở Command Prompt as Administrator trong VM cuckoo1

REM Set IP tĩnh *************02
netsh interface ip set address name="Local Area Connection" static *************02 ************* *************

REM Set DNS servers
netsh interface ip set dns name="Local Area Connection" static *******
netsh interface ip add dns name="Local Area Connection" ******* index=2

REM Flush DNS cache
ipconfig /flushdns

REM Test connectivity
ping -n 1 *************
ping -n 1 *******
ping -n 1 google.com

REM Test DNS resolution
nslookup google.com
nslookup github.com

REM Check final configuration
ipconfig /all
```

## BƯỚC 6: Test Internet Access (💻 CHẠY TRONG CẢ 2 VMs)

```cmd
REM Complete internet test
echo === Internet Connectivity Test ===

REM 1. Test local network
echo 1. Testing local network...
ping -n 1 *************

REM 2. Test internet IPs
echo 2. Testing internet IPs...
ping -n 1 *******
ping -n 1 *******

REM 3. Test DNS resolution
echo 3. Testing DNS resolution...
nslookup google.com
nslookup github.com
nslookup microsoft.com

REM 4. Test HTTP (if available)
echo 4. Testing HTTP...
curl -I http://google.com

REM 5. Test HTTPS (if available)
echo 5. Testing HTTPS...
curl -I https://google.com

echo === Test completed ===
```

## BƯỚC 7: Verify từ Host (🖥️ CHẠY TRÊN HOST)

```bash
# Check NAT network status
sudo virsh net-list --all
sudo virsh net-dumpxml cape-nat

# Check VM connectivity from host
ping -c 1 *************01
ping -c 1 *************02

# Check iptables rules
echo "Current NAT rules:"
sudo iptables -t nat -L -n | grep 192.168.100

echo "Current FORWARD rules:"
sudo iptables -L FORWARD -n | grep 192.168.100

# Test host internet
ping -c 1 *******
nslookup google.com
```

## BƯỚC 8: Troubleshooting (nếu cần)

### Nếu VMs không có internet:

```bash
# Trên host - check network
sudo virsh net-dumpxml cape-nat | grep forward
ip route show | grep 192.168.100

# Restart network
sudo virsh net-destroy cape-nat
sudo virsh net-start cape-nat
```

```cmd
REM Trong VMs - reset network
netsh winsock reset
netsh int ip reset
ipconfig /flushdns
ipconfig /release
ipconfig /renew
```

### Nếu DNS không work:

```cmd
REM Trong VMs - fix DNS
netsh interface ip set dns name="Ethernet" static *******
netsh interface ip add dns name="Ethernet" ******* index=2
ipconfig /flushdns
nslookup google.com
```

## TÓM TẮT LỆNH NHANH:

### 🖥️ Host (Linux):
```bash
# Tạo NAT network
sudo virsh net-destroy cape-hostonly
cat > /tmp/cape-nat.xml << 'EOF'
<network><name>cape-nat</name><forward mode='nat'><nat><port start='1024' end='65535'/></nat></forward><bridge name='virbr1' stp='on' delay='0'/><ip address='*************' netmask='*************'><dhcp><range start='**************' end='***************'/></dhcp></ip></network>
EOF
sudo virsh net-define /tmp/cape-nat.xml && sudo virsh net-start cape-nat

# Update VMs
sudo virsh shutdown cape1 cuckoo1 && sleep 15
sudo virsh attach-interface cape1 network cape-nat --model virtio --config
sudo virsh attach-interface cuckoo1 network cape-nat --model virtio --config
sudo virsh start cape1 cuckoo1

# NAT rules
sudo iptables -t nat -A POSTROUTING -s *************/24 -o enp4s0 -j MASQUERADE
sudo iptables -A FORWARD -s *************/24 -o enp4s0 -j ACCEPT
sudo iptables-save | sudo tee /etc/iptables/rules.v4
```

### 💻 cape1 (Windows 10):
```cmd
REM Set IP tĩnh *************01
netsh interface ip set address name="Ethernet" static *************01 ************* *************
netsh interface ip set dns name="Ethernet" static *******
ipconfig /flushdns
ping google.com
```

### 💻 cuckoo1 (Windows 7):
```cmd
REM Set IP tĩnh *************02
netsh interface ip set address name="Local Area Connection" static *************02 ************* *************
netsh interface ip set dns name="Local Area Connection" static *******
ipconfig /flushdns
ping google.com
```

## KẾT QUẢ MONG ĐỢI:

✅ **Sau khi restore:**
- VMs ping được ******* ✓
- VMs resolve được DNS (google.com) ✓  
- VMs truy cập được HTTP/HTTPS ✓
- VMs có thể download/update ✓
- VMs vẫn ping được host ✓

❌ **Trước khi restore:**
- VMs chỉ ping được local network
- Không resolve DNS
- Không truy cập internet

**VMs sẽ có internet access trở lại hoàn toàn!** 🌐
